<script type="importmap">
  {
    "imports": {
      "@google/genai": "https://esm.sh/@google/genai@^1.4.0"
    }
  }
</script>
<h1>Veo - Video Generation</h1>
<p>Use Veo to generate high-definition video from text and image prompts.</p>
<div class="form-container">
  <div class="prompt-wrapper">
    <textarea id="prompt-input" type="text" placeholder="Enter a prompt, e.g., 'An astronaut riding a horse on Mars'" rows="3"></textarea>
    <button id="optimize-button" title="Use AI to improve your prompt">✨ Optimize</button>
  </div>
  <p>
    <label for="aspect-ratio-select">Aspect Ratio:</label>
    <select id="aspect-ratio-select">
      <option value="16:9">16:9 (Landscape)</option>
      <option value="9:16">9:16 (Portrait)</option>
      <option value="1:1">1:1 (Square)</option>
      <option value="4:3">4:3</option>
      <option value="3:4">3:4</option>
    </select>
  </p>
  <p>Optionally, provide an image to guide the video generation.</p>
  <p><input id="file-input" type="file" accept="image/*" /></p>
  <img alt="Conditioning image preview" id="img" />
  <div id="prompt-suggestions" style="display: none;">
      <h4>常用图生视频提示词</h4>
      <div class="suggestions-grid">
          <button class="suggestion-button">让照片里的人物动起来，眼神流转，微笑</button>
          <button class="suggestion-button">为这张肖像添加微妙的动态，如头发被微风吹拂</button>
          <button class="suggestion-button">让图中的人物完成这个动作，使其流畅自然</button>
          <button class="suggestion-button">把这张静态图片变成一个连贯的动作视频</button>
      </div>
  </div>
  <button id="generate-button">Generate Video</button>
</div>
<div class="output-container">
  <video id="video" autoplay loop controls></video>
  <p id="status">Ready.</p>
</div><link rel="stylesheet" href="/index.css">
<script type="module" src="/index.tsx"></script>

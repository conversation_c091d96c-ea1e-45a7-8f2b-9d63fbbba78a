/* tslint:disable */
/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import {GenerateVideosParameters, GoogleGenAI} from '@google/genai';

const GEMINI_API_KEY = process.env.API_KEY;

async function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

function blobToBase64(blob: Blob) {
  return new Promise<string>(async (resolve) => {
    const reader = new FileReader();
    reader.onload = () => {
      const url = reader.result as string;
      resolve(url.split(',')[1]);
    };
    reader.readAsDataURL(blob);
  });
}

function downloadFile(url, filename) {
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  a.style.display = 'none';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

async function generateContent(prompt: string, imageBytes: string, aspectRatio: string) {
  const ai = new GoogleGenAI({apiKey: GEMINI_API_KEY});

  const config: GenerateVideosParameters = {
    model: 'veo-2.0-generate-001',
    prompt,
    config: {
      numberOfVideos: 1,
      aspectRatio: aspectRatio as "16:9" | "9:16" | "1:1" | "4:3" | "3:4",
    },
  };

  if (imageBytes) {
    config.image = {
      imageBytes,
      mimeType: 'image/png',
    };
  }

  let operation = await ai.models.generateVideos(config);
  
  const statusEl = document.querySelector('#status') as HTMLDivElement;
  const loadingMessages = [
    "Warming up the pixels...",
    "Choreographing the photons...",
    "Teaching the bits to dance...",
    "Assembling the digital dream...",
    "Almost there, polishing the final frames...",
  ];
  let messageIndex = 0;
  const intervalId = setInterval(() => {
    statusEl.innerText = loadingMessages[messageIndex % loadingMessages.length];
    messageIndex++;
  }, 4000);


  while (!operation.done) {
    console.log('Waiting for completion');
    await delay(10000); // Poll every 10 seconds
    operation = await ai.operations.getVideosOperation({operation});
  }
  
  clearInterval(intervalId);

  const videos = operation.response?.generatedVideos;
  if (videos === undefined || videos.length === 0) {
    throw new Error('No videos were generated. Please try a different prompt.');
  }

  const videoEl = document.querySelector('#video') as HTMLVideoElement;
  videos.forEach(async (v, i) => {
    const url = decodeURIComponent(v.video.uri);
    const res = await fetch(`${url}&key=${GEMINI_API_KEY}`);
    const blob = await res.blob();
    const objectURL = URL.createObjectURL(blob);
    downloadFile(objectURL, `video-${i+1}.mp4`);
    videoEl.src = objectURL;
    console.log('Downloaded video', `video-${i+1}.mp4`);
    videoEl.style.display = 'block';
  });
}

const upload = document.querySelector('#file-input') as HTMLInputElement;
const promptSuggestions = document.querySelector('#prompt-suggestions') as HTMLDivElement;
const suggestionButtons = document.querySelectorAll('.suggestion-button');

let base64data = '';
let prompt = '';

upload.addEventListener('change', async (e) => {
  const file = (e.target as HTMLInputElement).files[0];
  const imgPreview = document.querySelector('#img') as HTMLImageElement;
  if (file) {
    base64data = await blobToBase64(file);
    imgPreview.src = URL.createObjectURL(file);
    imgPreview.style.display = 'block';
    promptSuggestions.style.display = 'block';
  } else {
    base64data = '';
    imgPreview.src = '';
    imgPreview.style.display = 'none';
    promptSuggestions.style.display = 'none';
  }
});

const promptEl = document.querySelector('#prompt-input') as HTMLTextAreaElement;
promptEl.addEventListener('input', async () => {
  prompt = promptEl.value;
});

suggestionButtons.forEach(button => {
    button.addEventListener('click', () => {
        const suggestionText = (button as HTMLButtonElement).innerText;
        promptEl.value = suggestionText;
        prompt = suggestionText;
        promptEl.focus();
    });
});

const statusEl = document.querySelector('#status') as HTMLDivElement;
const video = document.querySelector('#video') as HTMLVideoElement;
const aspectRatioSelect = document.querySelector('#aspect-ratio-select') as HTMLSelectElement;

const generateButton = document.querySelector(
  '#generate-button',
) as HTMLButtonElement;
const optimizeButton = document.querySelector(
  '#optimize-button',
) as HTMLButtonElement;

generateButton.addEventListener('click', (e) => {
  generate();
});

optimizeButton.addEventListener('click', (e) => {
  handleOptimizePrompt();
});


async function handleOptimizePrompt() {
  const currentPrompt = promptEl.value.trim();
  if (!currentPrompt) {
    return;
  }

  optimizeButton.disabled = true;
  generateButton.disabled = true;
  optimizeButton.innerText = 'Optimizing...';

  try {
    const ai = new GoogleGenAI({apiKey: GEMINI_API_KEY});
    const response = await ai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: currentPrompt,
        config: {
            systemInstruction: `You are a prompt engineer for an advanced text-to-video AI model called Veo. 
Your task is to rewrite the user's prompt to be more vivid, detailed, and cinematic. 
Focus on visual details, actions, atmosphere, and camera movements. 
Keep the core subject of the original prompt but enhance it significantly for the best possible video output. 
Respond only with the rewritten prompt, without any conversational preamble.`,
        }
    });
    
    const newPrompt = response.text.trim();
    
    promptEl.value = newPrompt;
    prompt = newPrompt;

  } catch (e) {
    console.error('Prompt optimization failed:', e);
    statusEl.innerText = `Error: Failed to optimize prompt. ${e.message}`;
  } finally {
    optimizeButton.disabled = false;
    generateButton.disabled = false;
    optimizeButton.innerText = '✨ Optimize';
  }
}


async function generate() {
  statusEl.innerText = 'Preparing your video...';
  video.style.display = 'none';

  generateButton.disabled = true;
  optimizeButton.disabled = true;
  upload.disabled = true;
  promptEl.disabled = true;
  aspectRatioSelect.disabled = true;

  try {
    if (!prompt.trim()) {
      throw new Error('Please enter a prompt.');
    }
    if (!GEMINI_API_KEY) {
      throw new Error('API key not found. Please configure the API_KEY environment variable.');
    }
    const aspectRatio = aspectRatioSelect.value;
    await generateContent(prompt, base64data, aspectRatio);
    statusEl.innerText = 'Done! Your video has been downloaded.';
  } catch (e) {
    console.error('Video generation failed:', e);
    statusEl.innerText = `Error: ${e.message}`;
  }

  generateButton.disabled = false;
  optimizeButton.disabled = false;
  upload.disabled = false;
  promptEl.disabled = false;
  aspectRatioSelect.disabled = false;
}
body {
  font-family: 'Google Sans', sans-serif, system-ui;
  padding: 20px;
  background-color: #f8f9fa;
  color: #3c4043;
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 800px;
  margin: 0 auto;
}

h1 {
  color: #1a73e8;
}

input, button, textarea, select {
  font: inherit;
  border-radius: 8px;
  border: 1px solid #dadce0;
  padding: 12px;
  box-sizing: border-box;
}

input[type="file"] {
  border: none;
}

select {
  width: 100%;
  appearance: none;
  background-color: white;
  background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%235f6368%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E');
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: .65em auto;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #5f6368;
}

img, video {
  display: none;
  width: 100%;
  max-width: 512px;
  border-radius: 8px;
  margin-top: 16px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
}

video {
  display: none;
}

textarea {
  width: 100%;
  resize: vertical;
}

button {
  background-color: #1a73e8;
  color: white;
  font-weight: bold;
  cursor: pointer;
  border: none;
  transition: background-color 0.3s;
  width: 100%;
  margin-top: 16px;
}

button:hover {
  background-color: #185abc;
}

button:disabled {
  background-color: #e0e0e0;
  cursor: not-allowed;
}

.form-container, .output-container {
  background-color: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  width: 100%;
  margin-top: 20px;
}

p {
  line-height: 1.6;
}

#status {
  margin-top: 20px;
  font-weight: 500;
  color: #5f6368;
}

#prompt-suggestions {
  margin-top: 20px;
}

#prompt-suggestions h4 {
  margin-top: 0;
  margin-bottom: 12px;
  color: #3c4043;
}

.suggestions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.suggestion-button {
  background-color: #f1f3f4;
  color: #3c4043;
  border: 1px solid #dadce0;
  padding: 8px 12px;
  text-align: left;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s, box-shadow 0.2s;
  width: 100%;
  height: 100%;
  margin-top: 0;
}

.suggestion-button:hover {
  background-color: #e8eaed;
  box-shadow: 0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15);
}

.prompt-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  width: 100%;
  margin-bottom: 1rem;
}

#prompt-input {
  flex-grow: 1;
}

#optimize-button {
  margin-top: 0;
  width: auto;
  padding: 12px 16px;
  background-color: #e8f0fe;
  color: #1967d2;
  font-weight: 500;
}

#optimize-button:hover {
  background-color: #d2e3fc;
}

#optimize-button:disabled {
  background-color: #e0e0e0;
  color: #9e9e9e;
}